name: Deploy To Test

on:
  workflow_dispatch:
    inputs:
      env:
        description: 'Select deployment environment'
        required: true
        default: 'test-1'
        type: choice
        options:
          - test-1
          - test-2

jobs:
  deploy-next:
    uses: Opendashboard-Inc/opendashboard-workflow/.github/workflows/deploy-node.yaml@main
    with:
      NAME: opendashboard-frontend-test
      PROJECT_PATH: ${{ github.event.inputs.env == 'test-1' && '/var/www/test-1.opendashboard.app/node_root' || '/var/www/test-2.opendashboard.app/node_root' }}
      ENV_FILE_PATH: ${{ github.event.inputs.env == 'test-1' && 'opendashboard-frontend/test-1.env' || 'opendashboard-frontend/test-2.env' }}
      BUILD_DIR_NAME: ".next"
    secrets:
      SSH_HOST: ${{ vars.STAGE_HOST }}
      SSH_KEY: ${{ secrets.SSH_KEY }}
      SSH_USER: ${{ secrets.SSH_USER }}
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
