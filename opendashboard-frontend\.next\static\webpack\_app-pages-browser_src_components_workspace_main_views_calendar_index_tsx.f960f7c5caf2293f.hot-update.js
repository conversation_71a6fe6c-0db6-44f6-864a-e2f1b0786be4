"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _components_DayView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/DayView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\");\n/* harmony import */ var _components_WeekView__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/WeekView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\");\n/* harmony import */ var _components_MonthView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/MonthView */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\");\n/* harmony import */ var _components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./components/CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom modifier to restrict dragging to the calendar main content area only\nconst restrictToCalendarContainer = (param)=>{\n    let { transform, draggingNodeRect, windowRect } = param;\n    if (!draggingNodeRect || !windowRect) {\n        return transform;\n    }\n    // Find the calendar main content container (the div that contains the views)\n    const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\n    if (!calendarContainer) {\n        return transform;\n    }\n    const containerRect = calendarContainer.getBoundingClientRect();\n    // Calculate the boundaries relative to the window\n    const minX = containerRect.left;\n    const maxX = containerRect.right - draggingNodeRect.width;\n    const minY = containerRect.top;\n    const maxY = containerRect.bottom - draggingNodeRect.height;\n    // Get current pointer position\n    const currentX = transform.x + draggingNodeRect.left;\n    const currentY = transform.y + draggingNodeRect.top;\n    // Constrain the position\n    const constrainedX = Math.min(Math.max(currentX, minX), maxX);\n    const constrainedY = Math.min(Math.max(currentY, minY), maxY);\n    return {\n        ...transform,\n        x: constrainedX - draggingNodeRect.left,\n        y: constrainedY - draggingNodeRect.top\n    };\n};\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only refresh if the peek view was open and is now closed.\n        if (prevPeekRecordId && !peekRecordId) {\n            console.log(\"Peek view was closed, refreshing calendar data\");\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 175,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width: (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width,\n                height: (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        console.log(\"[DragEnd] Fired\", {\n            active,\n            over\n        });\n        if (!over || !active || !canEditData || active.id === over.id) {\n            console.log(\"[DragEnd] Exiting: Invalid drop conditions.\", {\n                over,\n                active,\n                canEditData\n            });\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            console.log(\"[DragEnd] Exiting: Missing active or over data.\");\n            return;\n        }\n        console.log(\"[DragEnd] Active Data:\", activeData);\n        console.log(\"[DragEnd] Over Data:\", overData);\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        console.log(\"[DragEnd] Event to Update:\", eventToUpdate);\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(originalEnd, originalStart);\n        console.log(\"[DragEnd] Original Times:\", {\n            originalStart,\n            originalEnd,\n            duration\n        });\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            console.log(\"[DragEnd] Logic branch: All-day\");\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n            // For all-day drops, normalize the time to the start of the day\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type.startsWith(\"timeslot\")) {\n            console.log(\"[DragEnd] Logic branch: Timeslot\");\n            newStart = new Date(overData.date);\n            // Enhanced Minute-level precision calculation\n            const grabOffsetY = activeData.grabOffsetY || 0;\n            const slotTop = over.rect.top;\n            const pointerY = pointerCoordinates.current.y;\n            // Adjust pointer position based on initial grab point\n            const adjustedPointerY = pointerY - grabOffsetY;\n            const offsetY = adjustedPointerY - slotTop;\n            const slotHeight = over.rect.height; // Should be 60px\n            const minutesFromTop = offsetY / slotHeight * 60;\n            // Snap to nearest 5-minute interval\n            const snappedMinutes = Math.round(minutesFromTop / 5) * 5;\n            const newMinutes = Math.max(0, Math.min(59, snappedMinutes));\n            newStart.setHours(overData.hour, newMinutes, originalStart.getSeconds(), originalStart.getMilliseconds());\n            console.log(\"[DragEnd] Minute Precision Calc:\", {\n                slotTop,\n                pointerY,\n                offsetY,\n                slotHeight,\n                minutesFromTop,\n                newMinutes\n            });\n        } else if (overData.type === \"daycell\") {\n            console.log(\"[DragEnd] Logic branch: Day Cell (Month)\");\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(originalStart, dayDifference);\n        } else {\n            console.log(\"[DragEnd] Exiting: Invalid drop target type.\");\n            return; // Invalid drop target\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        console.log(\"[DragEnd] New Times:\", {\n            newStart,\n            newEnd\n        });\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        console.log(\"[DragEnd] Update Payload:\", {\n            recordId,\n            newValues\n        });\n        try {\n            console.log(\"[DragEnd] Calling updateRecordValues...\");\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            console.log(\"[DragEnd] Update successful.\");\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to update event.\");\n            console.error(\"[DragEnd] Error updating event:\", error);\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting event:\", error);\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 492,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DayView__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeekView__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MonthView__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CalendarEventItem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: \"month\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 769,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 685,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 491,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"gC+3ORgbOSOWj17lR2zBDE1vWrs=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_16__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_17__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewSelection,\n        usePrevious,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_18__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});