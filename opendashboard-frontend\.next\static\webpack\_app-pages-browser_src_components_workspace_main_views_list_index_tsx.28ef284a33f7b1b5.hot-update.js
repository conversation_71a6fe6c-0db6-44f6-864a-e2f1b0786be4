"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_list_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/form/components/element/buttonGroup.tsx":
/*!*************************************************************************************!*\
  !*** ./src/components/workspace/main/views/form/components/element/buttonGroup.tsx ***!
  \*************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ButtonGroupFieldArea: function() { return /* binding */ ButtonGroupFieldArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/custom-ui/forceRender */ \"(app-pages-browser)/./src/components/custom-ui/forceRender.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _providers_broadcast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/providers/broadcast */ \"(app-pages-browser)/./src/providers/broadcast.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/buttonActionHelpers */ \"(app-pages-browser)/./src/utils/buttonActionHelpers.ts\");\n/* harmony import */ var _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/buttonAction */ \"(app-pages-browser)/./src/utils/buttonAction.ts\");\n/* __next_internal_client_entry_do_not_use__ ButtonGroupFieldArea auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ButtonGroupFieldArea = (props)=>{\n    _s();\n    const { updateRecordValues, deleteRecords, directDeleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews)();\n    const { confirm, toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_6__.useAlert)();\n    const { databaseStore, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    const { forceRender } = (0,_components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { token, user } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { sendMessage } = (0,_providers_broadcast__WEBPACK_IMPORTED_MODULE_11__.useBroadcast)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord)();\n    const inputDialog = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.useInputDialog)();\n    const { id, columnsMap, databaseId, values } = props;\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_15__.useStackedPeek)(databaseId);\n    const column = columnsMap[id];\n    const buttons = column.buttons || [];\n    const database = databaseStore === null || databaseStore === void 0 ? void 0 : databaseStore[databaseId];\n    if (!database) {\n        return null;\n    }\n    const row = {\n        id: String(values.id || \"\"),\n        recordValues: values\n    };\n    const handleButtonClick = async (button)=>{\n        var _button_actions;\n        if (!(button === null || button === void 0 ? void 0 : (_button_actions = button.actions) === null || _button_actions === void 0 ? void 0 : _button_actions.length)) {\n            toast.info(\"This button has no actions configured\");\n            return;\n        }\n        const context = {\n            record: row,\n            database: database,\n            workspace: workspace,\n            token: token,\n            user: user,\n            databaseId,\n            // Manually add the parent record context if we are in a peek view\n            parentRecord: maybeRecord ? {\n                id: maybeRecord.recordInfo.record.id,\n                databaseId: maybeRecord.database.id\n            } : undefined\n        };\n        const services = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.createActionServices)({\n            updateRecordValues: updateRecordValues,\n            deleteRecords,\n            directDeleteRecords,\n            setPeekRecord: (recordId, databaseId)=>openRecord(recordId, databaseId, context.parentRecord),\n            confirm,\n            toast,\n            router,\n            forceRender,\n            sendMessage: (message)=>sendMessage(\"info\", \"action\", {\n                    message\n                })\n        }, inputDialog);\n        const urlsToOpen = [];\n        let actionSucceeded = true;\n        for (const action of button.actions){\n            const { success, result } = await (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.executeDeclarativeAction)(action, context, services, databaseStore);\n            if (!success) {\n                actionSucceeded = false;\n                break;\n            }\n            if (action.actionType === \"openUrl\" && (result === null || result === void 0 ? void 0 : result.url)) {\n                urlsToOpen.push(result.url);\n            }\n        }\n        if (urlsToOpen.length > 0) {\n            services.toast.success(\"Opening \".concat(urlsToOpen.length, \" URL(s)...\"));\n            urlsToOpen.forEach((url)=>{\n                window.open(url, \"_blank\", \"noopener,noreferrer\");\n            });\n        }\n        if (actionSucceeded) {} else {}\n        forceRender();\n    };\n    const buttonStates = buttons.map((button)=>({\n            button,\n            state: (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.evaluateButtonState)(button, values || {})\n        }));\n    const visibleButtons = buttons.filter((button)=>{\n        var _buttonStates_find;\n        const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n        return (buttonState === null || buttonState === void 0 ? void 0 : buttonState.visible) !== false;\n    });\n    if (!visibleButtons.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-wrap gap-2 button-group-container\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2 button-group-container\",\n                children: visibleButtons.length === 1 ? (()=>{\n                    var _buttonStates_find;\n                    const button = visibleButtons[0];\n                    const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                    const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.ERROR;\n                    const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.DISABLED;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>!isDisabled && !hasError && handleButtonClick(button),\n                        disabled: isDisabled || hasError,\n                        variant: \"outline\",\n                        className: \"text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center \".concat(hasError ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed\" : isDisabled ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed\" : \"\"),\n                        children: [\n                            hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                className: \"size-3 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 19\n                            }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                className: \"size-3 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 19\n                            }, undefined) : (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__.getButtonIcon)(button),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"truncate\",\n                                children: button.label || \"Action\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 35\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 15\n                    }, undefined);\n                })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-1 flex-wrap sm:flex-nowrap w-full\",\n                    children: [\n                        (()=>{\n                            var _buttonStates_find;\n                            const button = visibleButtons[0];\n                            const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                            const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.ERROR;\n                            const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.DISABLED;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>!isDisabled && !hasError && handleButtonClick(button),\n                                disabled: isDisabled || hasError,\n                                variant: \"outline\",\n                                className: \"text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center \".concat(hasError ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed\" : isDisabled ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed\" : \"\"),\n                                children: [\n                                    hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                        className: \"size-3 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 21\n                                    }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                        className: \"size-3 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 21\n                                    }, undefined) : (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__.getButtonIcon)(button),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"truncate\",\n                                        children: button.label || \"Action\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 17\n                            }, undefined);\n                        })(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"text-xs rounded-full p-1.5 h-auto\",\n                                        variant: \"outline\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"size-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    className: \"w-48 z-[99999]\",\n                                    sideOffset: 5,\n                                    children: visibleButtons.slice(1).map((button)=>{\n                                        var _buttonStates_find;\n                                        const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                                        const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.ERROR;\n                                        const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.DISABLED;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                            onClick: ()=>!isDisabled && !hasError && handleButtonClick(button),\n                                            disabled: isDisabled || hasError,\n                                            className: \"text-xs p-2 gap-2 flex items-center \".concat(hasError ? \"text-gray-600 cursor-not-allowed\" : isDisabled ? \"text-gray-400 cursor-not-allowed\" : \"\"),\n                                            children: [\n                                                hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                                    className: \"size-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 25\n                                                }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                                    className: \"size-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 25\n                                                }, undefined) : (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__.getButtonIcon)(button),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate\",\n                                                    children: button.label || \"Action\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, button.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.Dialog, {\n                open: inputDialog.inputDialogOpen,\n                onOpenChange: inputDialog.setInputDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogTitle, {\n                                children: inputDialog.inputDialogTitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: inputDialog.inputDialogMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    value: inputDialog.inputValue,\n                                    onChange: (e)=>inputDialog.setInputValue(e.target.value),\n                                    placeholder: \"Enter your input here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: ()=>inputDialog.setInputDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: inputDialog.handleInputSubmit,\n                                    children: \"Submit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ButtonGroupFieldArea, \"KAV3e7PZmvIF5Zhl7XwvdCaQeXk=\", false, function() {\n    return [\n        _providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews,\n        _providers_alert__WEBPACK_IMPORTED_MODULE_6__.useAlert,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace,\n        _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        _providers_user__WEBPACK_IMPORTED_MODULE_9__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _providers_broadcast__WEBPACK_IMPORTED_MODULE_11__.useBroadcast,\n        _providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord,\n        _utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.useInputDialog,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_15__.useStackedPeek\n    ];\n});\n_c = ButtonGroupFieldArea;\nvar _c;\n$RefreshReg$(_c, \"ButtonGroupFieldArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/form/components/element/buttonGroup.tsx\n"));

/***/ })

});