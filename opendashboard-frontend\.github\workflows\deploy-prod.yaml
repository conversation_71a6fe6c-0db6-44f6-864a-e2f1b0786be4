# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Deploy To Production

on:
  push:
    branches: [ main ]

jobs:
  deploy-next:
    uses: Opendashboard-Inc/opendashboard-workflow/.github/workflows/deploy-node.yaml@main
    with:
      PROJECT_PATH: ${{ vars.PROD_PATH }}
      ENV_FILE_PATH: ${{ vars.PROD_ENV_PATH }}
      NAME: opendashboard-frontend
      BUILD_DIR_NAME: ".next"
    secrets:
      SSH_HOST: ${{ vars.PROD_HOST }}
      SSH_KEY: ${{ secrets.SSH_KEY }}
      SSH_USER: ${{ secrets.SSH_USER }}
      GH_TOKEN: ${{ secrets.GH_TOKEN }}

  tag-version:
    needs: deploy-next
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: '0'
      - uses: anothrNick/github-tag-action@1.67.0
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          WITH_V: false
          CUSTOM_TAG: prod-1.0.${{github.run_number}}


